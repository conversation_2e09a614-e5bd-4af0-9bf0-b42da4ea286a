//
//  TabBarView.swift
//  Lmini
//
//  Created by MacBook Pro-Leo on 5/31/25.
//

import SwiftUI

struct TabBarView: View {
    @State private var selectedTab = 0
    
    var body: some View {
        TabView(selection: $selectedTab) {
            // 增强 - AI图像增强
            EnhanceTabView()
                .tabItem {
                    Image(systemName: "sparkles")
                    Text("增强")
                }
                .tag(0)
            
            // AI照片 - 滤镜和效果
            AIPhotoTabView()
                .tabItem {
                    Image(systemName: "camera.filters")
                    Text("AI照片")
                }
                .tag(1)
            
            // AI滤镜 - 艺术风格
            AIFilterTabView()
                .tabItem {
                    Image(systemName: "camera.macro.circle")
                    Text("AI滤镜")
                }
                .tag(2)
            
            // 历史记录
            HistoryView()
                .tabItem {
                    Image(systemName: "clock.arrow.circlepath")
                    Text("历史")
                }
                .tag(3)
            
            // 设置
            SettingsView()
                .tabItem {
                    Image(systemName: "gearshape")
                    Text("设置")
                }
                .tag(4)
        }
        .accentColor(.blue)
        .background(Color.black)
    }
}

// MARK: - 增强标签页
struct EnhanceTabView: View {
    @State private var selectedImage: UIImage?
    @State private var showingImagePicker = false
    @State private var showingCamera = false
    
    var body: some View {
        NavigationView {
            ZStack {
                Color.black.ignoresSafeArea()
                
                ScrollView {
                    VStack(spacing: 20) {
                        // 顶部标题区域
                        HStack {
                            VStack(alignment: .leading) {
                                Text("Lmini")
                                    .font(.largeTitle)
                                    .fontWeight(.bold)
                                    .foregroundColor(.white)
                                
                                Text("AI图像增强专家")
                                    .font(.subheadline)
                                    .foregroundColor(.gray)
                            }
                            
                            Spacer()
                            
                            Button(action: {}) {
                                Text("PRO")
                                    .font(.caption)
                                    .fontWeight(.bold)
                                    .foregroundColor(.white)
                                    .padding(.horizontal, 12)
                                    .padding(.vertical, 6)
                                    .background(Color.red)
                                    .cornerRadius(12)
                            }
                        }
                        .padding(.horizontal)
                        
                        // 功能分类区域
                        VStack(alignment: .leading, spacing: 16) {
                            // 基础增强
                            FeatureCategoryView(
                                title: "基础增强 ✨",
                                features: [
                                    FeatureItem(title: "图像增强", icon: "wand.and.stars", description: "提升图像清晰度"),
                                    FeatureItem(title: "降噪处理", icon: "camera.filters", description: "去除图像噪点"),
                                    FeatureItem(title: "锐化处理", icon: "camera.macro", description: "增强图像细节")
                                ]
                            )
                            
                            // 高级功能
                            FeatureCategoryView(
                                title: "高级功能 🚀",
                                features: [
                                    FeatureItem(title: "超分辨率", icon: "plus.magnifyingglass", description: "提升图像分辨率"),
                                    FeatureItem(title: "色彩增强", icon: "paintpalette", description: "优化色彩表现"),
                                    FeatureItem(title: "光线修复", icon: "sun.max", description: "修复曝光问题")
                                ]
                            )
                        }
                        .padding(.horizontal)
                        
                        // 快速操作按钮
                        HStack(spacing: 20) {
                            QuickActionButton(
                                title: "照片",
                                icon: "photo",
                                action: { showingImagePicker = true }
                            )
                            
                            QuickActionButton(
                                title: "相机",
                                icon: "camera",
                                action: { showingCamera = true }
                            )
                        }
                        .padding(.horizontal)
                        
                        Spacer(minLength: 100)
                    }
                }
            }
            .navigationBarHidden(true)
        }
        .sheet(isPresented: $showingImagePicker) {
            ImagePicker(selectedImage: $selectedImage, sourceType: .photoLibrary)
        }
        .sheet(isPresented: $showingCamera) {
            ImagePicker(selectedImage: $selectedImage, sourceType: .camera)
        }
    }
}

// MARK: - AI照片标签页
struct AIPhotoTabView: View {
    var body: some View {
        NavigationView {
            ZStack {
                Color.black.ignoresSafeArea()
                
                ScrollView {
                    VStack(spacing: 20) {
                        Text("AI照片处理")
                            .font(.largeTitle)
                            .fontWeight(.bold)
                            .foregroundColor(.white)
                            .padding()
                        
                        // 这里可以添加AI照片相关功能
                        FilterSelectionView()
                    }
                }
            }
            .navigationBarHidden(true)
        }
    }
}

// MARK: - AI滤镜标签页
struct AIFilterTabView: View {
    var body: some View {
        NavigationView {
            ZStack {
                Color.black.ignoresSafeArea()
                
                ScrollView {
                    VStack(spacing: 20) {
                        Text("AI滤镜")
                            .font(.largeTitle)
                            .fontWeight(.bold)
                            .foregroundColor(.white)
                            .padding()
                        
                        // 这里可以添加AI滤镜相关功能
                        Text("敬请期待...")
                            .foregroundColor(.gray)
                    }
                }
            }
            .navigationBarHidden(true)
        }
    }
}

// MARK: - 功能分类视图
struct FeatureCategoryView: View {
    let title: String
    let features: [FeatureItem]
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text(title)
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(.white)
            
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 3), spacing: 12) {
                ForEach(features, id: \.title) { feature in
                    FeatureCard(feature: feature)
                }
            }
        }
    }
}

// MARK: - 功能卡片
struct FeatureCard: View {
    let feature: FeatureItem
    
    var body: some View {
        VStack(spacing: 8) {
            Image(systemName: feature.icon)
                .font(.system(size: 24))
                .foregroundColor(.blue)
            
            Text(feature.title)
                .font(.caption)
                .fontWeight(.medium)
                .foregroundColor(.white)
                .multilineTextAlignment(.center)
            
            Text(feature.description)
                .font(.caption2)
                .foregroundColor(.gray)
                .multilineTextAlignment(.center)
        }
        .frame(height: 80)
        .frame(maxWidth: .infinity)
        .background(Color.gray.opacity(0.1))
        .cornerRadius(12)
    }
}

// MARK: - 快速操作按钮
struct QuickActionButton: View {
    let title: String
    let icon: String
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack {
                Image(systemName: icon)
                    .font(.system(size: 18))
                Text(title)
                    .font(.system(size: 16, weight: .medium))
            }
            .foregroundColor(.white)
            .frame(maxWidth: .infinity)
            .padding(.vertical, 12)
            .background(Color.gray.opacity(0.2))
            .cornerRadius(25)
        }
    }
}

// MARK: - 功能项目模型
struct FeatureItem {
    let title: String
    let icon: String
    let description: String
}

#Preview {
    TabBarView()
}
